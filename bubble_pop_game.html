<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>射击定位训练器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Courier New', monospace;
            color: white;
            overflow: hidden;
            cursor: crosshair;
        }
        
        #hud {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff00;
        }
        
        #gameArea {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: radial-gradient(circle at center, rgba(0,255,0,0.1) 0%, transparent 70%);
        }
        
        .crosshair {
            position: fixed;
            width: 30px;
            height: 30px;
            pointer-events: none;
            z-index: 999;
            transform: translate(-50%, -50%);
        }
        
        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: #00ff00;
        }
        
        .crosshair::before {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .crosshair::after {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .target {
            position: absolute;
            border-radius: 50%;
            cursor: crosshair;
            transition: all 0.1s ease;
            border: 3px solid;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            user-select: none;
        }
        
        .target.small {
            width: 40px;
            height: 40px;
            border-color: #ff4444;
            background: radial-gradient(circle, #ff4444, #cc0000);
            font-size: 12px;
        }
        
        .target.medium {
            width: 60px;
            height: 60px;
            border-color: #ffaa00;
            background: radial-gradient(circle, #ffaa00, #cc8800);
            font-size: 14px;
        }
        
        .target.large {
            width: 80px;
            height: 80px;
            border-color: #00ff00;
            background: radial-gradient(circle, #00ff00, #00cc00);
            font-size: 16px;
        }
        
        .target:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px currentColor;
        }
        
        .hit-animation {
            animation: hit 0.3s ease-out forwards;
        }
        
        @keyframes hit {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.3); opacity: 0.8; }
            100% { transform: scale(0); opacity: 0; }
        }
        
        .miss-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff0000;
            border-radius: 50%;
            animation: miss 0.5s ease-out forwards;
            pointer-events: none;
        }
        
        @keyframes miss {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }
        
        #settings {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff00;
        }
        
        .difficulty-btn {
            background: #333;
            color: white;
            border: 1px solid #666;
            padding: 5px 10px;
            margin: 2px;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .difficulty-btn.active {
            background: #00ff00;
            color: black;
        }
        
        #stats {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff00;
        }

        .combo-display {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            font-weight: bold;
            color: #ffff00;
            text-shadow: 0 0 20px #ffff00;
            pointer-events: none;
            z-index: 1001;
            animation: comboShow 1s ease-out forwards;
        }

        @keyframes comboShow {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
        }

        .moving-target {
            animation: moveTarget 3s linear infinite;
        }

        @keyframes moveTarget {
            0% { transform: translateX(0); }
            50% { transform: translateX(100px); }
            100% { transform: translateX(0); }
        }

        .reaction-time {
            position: fixed;
            top: 50%;
            right: 20px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #00ff00;
            font-size: 14px;
        }

        .sound-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .sound-btn {
            background: #333;
            color: white;
            border: 1px solid #666;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
            font-size: 12px;
        }

        .sound-btn.active {
            background: #00ff00;
            color: black;
        }
    </style>
</head>
<body>
    <div class="crosshair" id="crosshair"></div>
    
    <div id="hud">
        <div>得分: <span id="score">0</span></div>
        <div>命中: <span id="hits">0</span></div>
        <div>射击: <span id="shots">0</span></div>
        <div>精度: <span id="accuracy">0%</span></div>
        <div>时间: <span id="timer">60</span>s</div>
    </div>
    
    <div id="settings">
        <div>难度:</div>
        <button class="difficulty-btn active" data-difficulty="easy">简单</button>
        <button class="difficulty-btn" data-difficulty="medium">中等</button>
        <button class="difficulty-btn" data-difficulty="hard">困难</button>
        <button class="difficulty-btn" data-difficulty="extreme">极限</button>
        <div style="margin-top: 10px;">
            <button id="startBtn">开始训练</button>
            <button id="pauseBtn">暂停</button>
            <button id="resetBtn">重置记录</button>
        </div>
        <div class="sound-controls">
            <span>音效:</span>
            <button class="sound-btn active" id="soundToggle">开启</button>
            <span>移动目标:</span>
            <button class="sound-btn" id="movingToggle">关闭</button>
        </div>
    </div>
    
    <div id="stats">
        <div>最佳精度: <span id="bestAccuracy">0%</span></div>
        <div>最高得分: <span id="bestScore">0</span></div>
        <div>连击: <span id="combo">0</span></div>
        <div>最大连击: <span id="maxCombo">0</span></div>
    </div>

    <div class="reaction-time" id="reactionTime" style="display: none;">
        <div>反应时间: <span id="avgReaction">0</span>ms</div>
        <div>最快反应: <span id="bestReaction">∞</span>ms</div>
    </div>
    
    <div id="gameArea"></div>
    
    <script>
        let gameState = {
            score: 0,
            hits: 0,
            shots: 0,
            combo: 0,
            maxCombo: localStorage.getItem('maxCombo') || 0,
            timeLeft: 60,
            isPlaying: false,
            isPaused: false,
            difficulty: 'easy',
            bestScore: localStorage.getItem('bestScore') || 0,
            bestAccuracy: localStorage.getItem('bestAccuracy') || 0,
            soundEnabled: true,
            movingTargets: false,
            reactionTimes: [],
            bestReaction: localStorage.getItem('bestReaction') || Infinity,
            targetSpawnTime: 0
        };
        
        const gameArea = document.getElementById('gameArea');
        const crosshair = document.getElementById('crosshair');
        
        // 难度配置
        const difficultyConfig = {
            easy: { spawnRate: 1200, targetLife: 3000, targetSizes: ['large', 'medium'] },
            medium: { spawnRate: 800, targetLife: 2000, targetSizes: ['medium', 'small'] },
            hard: { spawnRate: 500, targetLife: 1500, targetSizes: ['small'] },
            extreme: { spawnRate: 300, targetLife: 1000, targetSizes: ['small'] }
        };

        // 音效系统
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function playSound(frequency, duration, type = 'sine') {
            if (!gameState.soundEnabled) return;

            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }
        
        // 更新HUD
        function updateHUD() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('hits').textContent = gameState.hits;
            document.getElementById('shots').textContent = gameState.shots;
            document.getElementById('accuracy').textContent =
                gameState.shots > 0 ? Math.round((gameState.hits / gameState.shots) * 100) + '%' : '0%';
            document.getElementById('timer').textContent = gameState.timeLeft;
            document.getElementById('combo').textContent = gameState.combo;
            document.getElementById('maxCombo').textContent = gameState.maxCombo;
            document.getElementById('bestScore').textContent = gameState.bestScore;
            document.getElementById('bestAccuracy').textContent = gameState.bestAccuracy + '%';

            // 更新反应时间显示
            if (gameState.reactionTimes.length > 0) {
                const avgReaction = Math.round(gameState.reactionTimes.reduce((a, b) => a + b, 0) / gameState.reactionTimes.length);
                document.getElementById('avgReaction').textContent = avgReaction;
                document.getElementById('bestReaction').textContent =
                    gameState.bestReaction === Infinity ? '∞' : Math.round(gameState.bestReaction);
                document.getElementById('reactionTime').style.display = 'block';
            }
        }

        // 显示连击效果
        function showComboEffect(combo) {
            if (combo < 5) return;

            const comboDiv = document.createElement('div');
            comboDiv.className = 'combo-display';
            comboDiv.textContent = `${combo}x 连击!`;
            document.body.appendChild(comboDiv);

            setTimeout(() => {
                if (comboDiv.parentNode) {
                    comboDiv.parentNode.removeChild(comboDiv);
                }
            }, 1000);
        }
        
        // 创建目标
        function createTarget() {
            if (!gameState.isPlaying || gameState.isPaused) return;

            const target = document.createElement('div');
            const config = difficultyConfig[gameState.difficulty];
            const sizeType = config.targetSizes[Math.floor(Math.random() * config.targetSizes.length)];

            target.className = `target ${sizeType}`;

            // 添加移动效果
            if (gameState.movingTargets && Math.random() < 0.3) {
                target.classList.add('moving-target');
            }

            const size = sizeType === 'small' ? 40 : sizeType === 'medium' ? 60 : 80;
            const x = Math.random() * (window.innerWidth - size - 100); // 为移动留出空间
            const y = Math.random() * (window.innerHeight - size);

            target.style.left = x + 'px';
            target.style.top = y + 'px';

            const points = sizeType === 'small' ? 50 : sizeType === 'medium' ? 30 : 10;
            target.textContent = points;

            // 记录目标创建时间用于反应时间计算
            const spawnTime = Date.now();
            gameState.targetSpawnTime = spawnTime;

            target.addEventListener('click', function(e) {
                e.stopPropagation();
                const reactionTime = Date.now() - spawnTime;
                hitTarget(this, points, reactionTime);
            });

            gameArea.appendChild(target);

            // 自动消失
            setTimeout(() => {
                if (target.parentNode) {
                    target.parentNode.removeChild(target);
                }
            }, config.targetLife);
        }
        
        // 命中目标
        function hitTarget(target, points, reactionTime) {
            target.classList.add('hit-animation');
            gameState.hits++;
            gameState.combo++;

            // 更新最大连击
            if (gameState.combo > gameState.maxCombo) {
                gameState.maxCombo = gameState.combo;
                localStorage.setItem('maxCombo', gameState.maxCombo);
            }

            // 连击奖励
            const comboBonus = gameState.combo > 5 ? Math.floor(gameState.combo / 5) * 10 : 0;
            gameState.score += points + comboBonus;

            // 记录反应时间
            if (reactionTime) {
                gameState.reactionTimes.push(reactionTime);
                if (reactionTime < gameState.bestReaction) {
                    gameState.bestReaction = reactionTime;
                    localStorage.setItem('bestReaction', gameState.bestReaction);
                }
            }

            // 播放命中音效
            playSound(800 + (gameState.combo * 50), 0.1);

            // 显示连击效果
            if (gameState.combo % 5 === 0 && gameState.combo >= 5) {
                showComboEffect(gameState.combo);
                playSound(1200, 0.3, 'square'); // 连击特殊音效
            }

            setTimeout(() => {
                if (target.parentNode) {
                    target.parentNode.removeChild(target);
                }
            }, 300);

            updateHUD();
        }
        
        // 射击未命中
        function missShot(x, y) {
            gameState.shots++;
            gameState.combo = 0;

            // 播放未命中音效
            playSound(200, 0.2, 'sawtooth');

            const missIndicator = document.createElement('div');
            missIndicator.className = 'miss-indicator';
            missIndicator.style.left = (x - 10) + 'px';
            missIndicator.style.top = (y - 10) + 'px';

            gameArea.appendChild(missIndicator);

            setTimeout(() => {
                if (missIndicator.parentNode) {
                    missIndicator.parentNode.removeChild(missIndicator);
                }
            }, 500);

            updateHUD();
        }
        
        // 游戏计时器
        function startTimer() {
            const timer = setInterval(() => {
                if (!gameState.isPlaying || gameState.isPaused) return;
                
                gameState.timeLeft--;
                updateHUD();
                
                if (gameState.timeLeft <= 0) {
                    endGame();
                    clearInterval(timer);
                }
            }, 1000);
        }
        
        // 开始游戏
        function startGame() {
            gameState.isPlaying = true;
            gameState.isPaused = false;
            gameState.score = 0;
            gameState.hits = 0;
            gameState.shots = 0;
            gameState.combo = 0;
            gameState.timeLeft = 60;
            gameState.reactionTimes = [];

            // 清除现有目标
            document.querySelectorAll('.target').forEach(target => target.remove());
            document.querySelectorAll('.combo-display').forEach(combo => combo.remove());

            // 播放开始音效
            playSound(440, 0.3);
            playSound(554, 0.3);
            playSound(659, 0.3);

            updateHUD();
            startTimer();

            // 开始生成目标
            const spawnInterval = setInterval(() => {
                if (!gameState.isPlaying) {
                    clearInterval(spawnInterval);
                    return;
                }
                createTarget();
            }, difficultyConfig[gameState.difficulty].spawnRate);
        }
        
        // 结束游戏
        function endGame() {
            gameState.isPlaying = false;

            // 播放结束音效
            playSound(330, 0.5);
            playSound(262, 0.8);

            const accuracy = gameState.shots > 0 ? Math.round((gameState.hits / gameState.shots) * 100) : 0;
            const avgReaction = gameState.reactionTimes.length > 0 ?
                Math.round(gameState.reactionTimes.reduce((a, b) => a + b, 0) / gameState.reactionTimes.length) : 0;

            // 更新最佳记录
            if (gameState.score > gameState.bestScore) {
                gameState.bestScore = gameState.score;
                localStorage.setItem('bestScore', gameState.bestScore);
            }

            if (accuracy > gameState.bestAccuracy) {
                gameState.bestAccuracy = accuracy;
                localStorage.setItem('bestAccuracy', gameState.bestAccuracy);
            }

            updateHUD();

            let resultMessage = `训练结束！\n得分: ${gameState.score}\n精度: ${accuracy}%\n命中: ${gameState.hits}/${gameState.shots}`;
            if (gameState.maxCombo > 0) {
                resultMessage += `\n最大连击: ${gameState.maxCombo}`;
            }
            if (avgReaction > 0) {
                resultMessage += `\n平均反应时间: ${avgReaction}ms`;
            }

            alert(resultMessage);
        }
        
        // 鼠标移动跟踪准星
        document.addEventListener('mousemove', (e) => {
            crosshair.style.left = e.clientX + 'px';
            crosshair.style.top = e.clientY + 'px';
        });
        
        // 点击事件
        document.addEventListener('click', (e) => {
            if (!gameState.isPlaying || gameState.isPaused) return;
            
            // 检查是否点击了目标
            const clickedElement = document.elementFromPoint(e.clientX, e.clientY);
            if (!clickedElement || !clickedElement.classList.contains('target')) {
                missShot(e.clientX, e.clientY);
            } else {
                gameState.shots++;
            }
        });
        
        // 难度选择
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                gameState.difficulty = btn.dataset.difficulty;
            });
        });
        
        // 控制按钮
        document.getElementById('startBtn').addEventListener('click', startGame);
        document.getElementById('pauseBtn').addEventListener('click', () => {
            gameState.isPaused = !gameState.isPaused;
            document.getElementById('pauseBtn').textContent = gameState.isPaused ? '继续' : '暂停';
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            if (confirm('确定要重置所有记录吗？')) {
                localStorage.clear();
                gameState.bestScore = 0;
                gameState.bestAccuracy = 0;
                gameState.maxCombo = 0;
                gameState.bestReaction = Infinity;
                updateHUD();
                alert('记录已重置！');
            }
        });

        // 音效控制
        document.getElementById('soundToggle').addEventListener('click', function() {
            gameState.soundEnabled = !gameState.soundEnabled;
            this.textContent = gameState.soundEnabled ? '开启' : '关闭';
            this.classList.toggle('active', gameState.soundEnabled);
        });

        // 移动目标控制
        document.getElementById('movingToggle').addEventListener('click', function() {
            gameState.movingTargets = !gameState.movingTargets;
            this.textContent = gameState.movingTargets ? '开启' : '关闭';
            this.classList.toggle('active', gameState.movingTargets);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    if (!gameState.isPlaying) {
                        startGame();
                    } else {
                        gameState.isPaused = !gameState.isPaused;
                        document.getElementById('pauseBtn').textContent = gameState.isPaused ? '继续' : '暂停';
                    }
                    break;
                case 'r':
                case 'R':
                    if (!gameState.isPlaying) {
                        startGame();
                    }
                    break;
                case 'm':
                case 'M':
                    document.getElementById('soundToggle').click();
                    break;
            }
        });

        // 初始化
        updateHUD();
    </script>
</body>
</html>
