/* 射击定位训练器 - 样式文件 */

/* 基础样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    color: white;
    overflow: hidden;
    cursor: crosshair;
    user-select: none;
}

/* 游戏区域 */
#gameArea {
    width: 100vw;
    height: 100vh;
    position: relative;
    background: radial-gradient(circle at center, rgba(0,255,0,0.05) 0%, transparent 70%);
}

/* 准星样式 */
.crosshair {
    position: fixed;
    width: 30px;
    height: 30px;
    pointer-events: none;
    z-index: 999;
    transform: translate(-50%, -50%);
    opacity: 0.8;
}

.crosshair::before,
.crosshair::after {
    content: '';
    position: absolute;
    background: #00ff00;
    box-shadow: 0 0 5px #00ff00;
}

.crosshair::before {
    width: 2px;
    height: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.crosshair::after {
    width: 30px;
    height: 2px;
    top: 50%;
    transform: translateY(-50%);
}

/* 目标样式 */
.target {
    position: absolute;
    border-radius: 50%;
    cursor: crosshair;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    user-select: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.target.small {
    width: 40px;
    height: 40px;
    border-color: #ff4444;
    background: radial-gradient(circle, #ff6666, #ff4444, #cc0000);
    font-size: 12px;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.target.medium {
    width: 60px;
    height: 60px;
    border-color: #ffaa00;
    background: radial-gradient(circle, #ffcc44, #ffaa00, #cc8800);
    font-size: 14px;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.target.large {
    width: 80px;
    height: 80px;
    border-color: #00ff00;
    background: radial-gradient(circle, #44ff44, #00ff00, #00cc00);
    font-size: 16px;
    color: black;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
}

.target:hover {
    transform: scale(1.1);
    box-shadow: 0 0 25px currentColor, 0 4px 20px rgba(0,0,0,0.4);
}

/* 动画效果 */
.hit-animation {
    animation: hit 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes hit {
    0% { 
        transform: scale(1); 
        opacity: 1; 
    }
    30% { 
        transform: scale(1.4); 
        opacity: 0.9;
        filter: brightness(1.5);
    }
    100% { 
        transform: scale(0); 
        opacity: 0; 
    }
}

.miss-indicator {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #ff4444, #cc0000);
    border-radius: 50%;
    animation: miss 0.6s ease-out forwards;
    pointer-events: none;
    border: 2px solid #ff0000;
}

@keyframes miss {
    0% { 
        transform: scale(0); 
        opacity: 1; 
    }
    50% {
        transform: scale(1.5);
        opacity: 0.8;
    }
    100% { 
        transform: scale(3); 
        opacity: 0; 
    }
}

.moving-target {
    animation: moveTarget 3s ease-in-out infinite;
}

@keyframes moveTarget {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(80px) translateY(-20px); }
    50% { transform: translateX(160px); }
    75% { transform: translateX(80px) translateY(20px); }
}

/* 连击效果 */
.combo-display {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 48px;
    font-weight: bold;
    color: #ffff00;
    text-shadow: 0 0 30px #ffff00, 0 0 60px #ffaa00;
    pointer-events: none;
    z-index: 1001;
    animation: comboShow 1.2s ease-out forwards;
    font-family: 'Impact', 'Arial Black', sans-serif;
}

@keyframes comboShow {
    0% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.3) rotate(-10deg); 
    }
    30% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1.3) rotate(5deg); 
    }
    60% {
        transform: translate(-50%, -50%) scale(1.1) rotate(-2deg);
    }
    100% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(1) rotate(0deg); 
    }
}

/* UI面板基础样式 */
.panel {
    position: fixed;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #00ff00;
    box-shadow: 0 8px 32px rgba(0, 255, 0, 0.2);
    font-size: 14px;
    line-height: 1.6;
}

/* HUD面板 */
#hud {
    top: 20px;
    left: 20px;
    z-index: 1000;
    min-width: 200px;
}

#hud .stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 4px 0;
    border-bottom: 1px solid rgba(0, 255, 0, 0.2);
}

#hud .stat-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

#hud .stat-label {
    color: #aaa;
}

#hud .stat-value {
    color: #00ff00;
    font-weight: bold;
}

/* 设置面板 */
#settings {
    top: 20px;
    right: 20px;
    min-width: 220px;
}

.settings-section {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 255, 0, 0.2);
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-label {
    display: block;
    margin-bottom: 8px;
    color: #00ff00;
    font-weight: bold;
}

/* 按钮样式 */
.btn {
    background: linear-gradient(145deg, #333, #222);
    color: white;
    border: 1px solid #666;
    padding: 8px 16px;
    margin: 3px;
    cursor: pointer;
    border-radius: 6px;
    font-size: 12px;
    transition: all 0.2s ease;
    font-family: inherit;
}

.btn:hover {
    background: linear-gradient(145deg, #444, #333);
    border-color: #888;
    transform: translateY(-1px);
}

.btn.active {
    background: linear-gradient(145deg, #00ff00, #00cc00);
    color: black;
    border-color: #00ff00;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.4);
}

.btn.primary {
    background: linear-gradient(145deg, #0066cc, #0044aa);
    border-color: #0088ff;
}

.btn.primary:hover {
    background: linear-gradient(145deg, #0088ff, #0066cc);
}

.btn.danger {
    background: linear-gradient(145deg, #cc3333, #aa2222);
    border-color: #ff4444;
}

.btn.danger:hover {
    background: linear-gradient(145deg, #ff4444, #cc3333);
}

/* 控制按钮组 */
.control-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.sound-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.sound-controls span {
    color: #aaa;
    font-size: 12px;
}

/* 统计面板 */
#stats {
    bottom: 20px;
    left: 20px;
    min-width: 200px;
}

/* 反应时间面板 */
.reaction-time {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #00ff00;
    font-size: 14px;
    min-width: 180px;
    box-shadow: 0 8px 32px rgba(0, 255, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .panel {
        padding: 15px;
        font-size: 12px;
    }
    
    #hud, #settings, #stats {
        position: fixed;
        max-width: calc(100vw - 40px);
    }
    
    #settings {
        top: auto;
        bottom: 20px;
        right: 20px;
    }
    
    .reaction-time {
        display: none !important;
    }
    
    .combo-display {
        font-size: 36px;
    }
    
    .control-group {
        flex-direction: column;
    }
    
    .sound-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .panel {
        padding: 10px;
        font-size: 11px;
    }
    
    .target.small { width: 35px; height: 35px; font-size: 10px; }
    .target.medium { width: 50px; height: 50px; font-size: 12px; }
    .target.large { width: 65px; height: 65px; font-size: 14px; }
    
    .combo-display {
        font-size: 28px;
    }
}
