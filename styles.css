/* 射击定位训练器 - 样式文件 */

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 基础样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', 'Microsoft YaHei', sans-serif;
    color: white;
    overflow: hidden;
    cursor: crosshair;
    user-select: none;
    position: relative;
}

/* 动态背景粒子效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0,255,255,0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,0,255,0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(0,255,0,0.1), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes sparkle {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
}

/* 游戏区域 */
#gameArea {
    width: 100vw;
    height: 100vh;
    position: relative;
    background:
        radial-gradient(circle at center, rgba(0,255,255,0.03) 0%, transparent 70%),
        radial-gradient(circle at 30% 70%, rgba(255,0,255,0.02) 0%, transparent 50%);
    z-index: 2;
}

/* 准星样式 */
.crosshair {
    position: fixed;
    width: 40px;
    height: 40px;
    pointer-events: none;
    z-index: 999;
    transform: translate(-50%, -50%);
    opacity: 0.9;
    filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.8));
}

.crosshair::before,
.crosshair::after {
    content: '';
    position: absolute;
    background: linear-gradient(45deg, #00ffff, #00ff88, #88ff00);
    box-shadow:
        0 0 10px rgba(0, 255, 255, 0.6),
        inset 0 0 5px rgba(255, 255, 255, 0.3);
    border-radius: 1px;
}

.crosshair::before {
    width: 3px;
    height: 40px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(to bottom, transparent 35%, #00ffff 40%, #00ffff 60%, transparent 65%);
}

.crosshair::after {
    width: 40px;
    height: 3px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(to right, transparent 35%, #00ffff 40%, #00ffff 60%, transparent 65%);
}

/* 准星中心点 */
.crosshair::before {
    box-shadow:
        0 0 15px rgba(0, 255, 255, 0.8),
        0 0 5px rgba(255, 255, 255, 0.5);
}

/* 目标样式 */
.target {
    position: absolute;
    border-radius: 50%;
    cursor: crosshair;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: 4px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    user-select: none;
    box-shadow:
        0 8px 25px rgba(0,0,0,0.4),
        inset 0 2px 10px rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

/* 目标内部光效 */
.target::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.target.small {
    width: 40px;
    height: 40px;
    border-color: #ff6b6b;
    background:
        radial-gradient(circle at 30% 30%, #ff8e8e, #ff6b6b, #e55555, #cc4444);
    font-size: 12px;
    color: white;
    text-shadow:
        0 0 8px rgba(255,255,255,0.8),
        2px 2px 4px rgba(0,0,0,0.8);
    box-shadow:
        0 8px 25px rgba(255,107,107,0.4),
        inset 0 2px 10px rgba(255,255,255,0.3),
        0 0 20px rgba(255,107,107,0.6);
}

.target.medium {
    width: 60px;
    height: 60px;
    border-color: #ffa726;
    background:
        radial-gradient(circle at 30% 30%, #ffcc80, #ffa726, #ff9800, #e68900);
    font-size: 14px;
    color: white;
    text-shadow:
        0 0 8px rgba(255,255,255,0.8),
        2px 2px 4px rgba(0,0,0,0.8);
    box-shadow:
        0 8px 25px rgba(255,167,38,0.4),
        inset 0 2px 10px rgba(255,255,255,0.3),
        0 0 20px rgba(255,167,38,0.6);
}

.target.large {
    width: 80px;
    height: 80px;
    border-color: #66bb6a;
    background:
        radial-gradient(circle at 30% 30%, #a5d6a7, #66bb6a, #4caf50, #388e3c);
    font-size: 16px;
    color: white;
    text-shadow:
        0 0 8px rgba(255,255,255,0.8),
        2px 2px 4px rgba(0,0,0,0.8);
    box-shadow:
        0 8px 25px rgba(102,187,106,0.4),
        inset 0 2px 10px rgba(255,255,255,0.3),
        0 0 20px rgba(102,187,106,0.6);
}

.target:hover {
    transform: scale(1.15);
    box-shadow:
        0 0 40px currentColor,
        0 8px 30px rgba(0,0,0,0.5),
        inset 0 2px 15px rgba(255,255,255,0.4);
    filter: brightness(1.2) saturate(1.3);
}

/* 动画效果 */
.hit-animation {
    animation: hit 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes hit {
    0% { 
        transform: scale(1); 
        opacity: 1; 
    }
    30% { 
        transform: scale(1.4); 
        opacity: 0.9;
        filter: brightness(1.5);
    }
    100% { 
        transform: scale(0); 
        opacity: 0; 
    }
}

.miss-indicator {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #ff4444, #cc0000);
    border-radius: 50%;
    animation: miss 0.6s ease-out forwards;
    pointer-events: none;
    border: 2px solid #ff0000;
}

@keyframes miss {
    0% { 
        transform: scale(0); 
        opacity: 1; 
    }
    50% {
        transform: scale(1.5);
        opacity: 0.8;
    }
    100% { 
        transform: scale(3); 
        opacity: 0; 
    }
}

.moving-target {
    animation: moveTarget 3s ease-in-out infinite;
}

@keyframes moveTarget {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(80px) translateY(-20px); }
    50% { transform: translateX(160px); }
    75% { transform: translateX(80px) translateY(20px); }
}

/* 连击效果 */
.combo-display {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 56px;
    font-weight: 900;
    font-family: 'Orbitron', 'Impact', sans-serif;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow:
        0 0 30px rgba(255, 255, 255, 0.8),
        0 0 60px rgba(255, 107, 107, 0.6),
        0 0 90px rgba(72, 219, 251, 0.4);
    pointer-events: none;
    z-index: 1001;
    animation: comboShow 1.5s ease-out forwards, gradientShift 1.5s ease-in-out;
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
}

@keyframes comboShow {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.2) rotate(-15deg);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.4) rotate(8deg);
    }
    40% {
        transform: translate(-50%, -50%) scale(1.1) rotate(-3deg);
    }
    60% {
        transform: translate(-50%, -50%) scale(1.2) rotate(2deg);
    }
    80% {
        transform: translate(-50%, -50%) scale(1.05) rotate(-1deg);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* UI面板基础样式 */
.panel {
    position: fixed;
    background:
        linear-gradient(145deg,
            rgba(15, 25, 35, 0.95) 0%,
            rgba(25, 35, 50, 0.9) 50%,
            rgba(15, 25, 35, 0.95) 100%);
    backdrop-filter: blur(15px) saturate(1.2);
    padding: 24px;
    border-radius: 20px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 8px 32px rgba(0, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    font-size: 14px;
    line-height: 1.6;
    font-family: 'Rajdhani', sans-serif;
    position: relative;
    z-index: 100;
}

/* 面板边框光效 */
.panel::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #00ffff 0%,
        #0080ff 25%,
        #8000ff 50%,
        #ff0080 75%,
        #00ffff 100%);
    border-radius: 22px;
    z-index: -1;
    animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* HUD面板 */
#hud {
    top: 20px;
    left: 20px;
    z-index: 1000;
    min-width: 220px;
}

#hud .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid #00ffff;
    transition: all 0.3s ease;
}

#hud .stat-item:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateX(5px);
}

#hud .stat-item:last-child {
    margin-bottom: 0;
}

#hud .stat-label {
    color: #b0bec5;
    font-weight: 500;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#hud .stat-value {
    color: #00ffff;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 设置面板 */
#settings {
    top: 20px;
    right: 20px;
    min-width: 260px;
}

.settings-section {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(0, 100, 200, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(0, 150, 255, 0.2);
    transition: all 0.3s ease;
}

.settings-section:hover {
    background: rgba(0, 100, 200, 0.1);
    border-color: rgba(0, 150, 255, 0.4);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-label {
    display: block;
    margin-bottom: 12px;
    color: #64b5f6;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 8px rgba(100, 181, 246, 0.3);
}

/* 按钮样式 */
.btn {
    background: linear-gradient(145deg,
        rgba(50, 60, 80, 0.9) 0%,
        rgba(30, 40, 60, 0.9) 50%,
        rgba(20, 30, 50, 0.9) 100%);
    color: #e3f2fd;
    border: 2px solid rgba(100, 181, 246, 0.3);
    padding: 10px 18px;
    margin: 4px;
    cursor: pointer;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    background: linear-gradient(145deg,
        rgba(70, 80, 100, 0.9) 0%,
        rgba(50, 60, 80, 0.9) 50%,
        rgba(40, 50, 70, 0.9) 100%);
    border-color: rgba(100, 181, 246, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn.active {
    background: linear-gradient(145deg,
        rgba(0, 255, 150, 0.9) 0%,
        rgba(0, 200, 120, 0.9) 50%,
        rgba(0, 150, 100, 0.9) 100%);
    color: #000;
    border-color: #00ff88;
    box-shadow:
        0 0 20px rgba(0, 255, 136, 0.4),
        0 4px 15px rgba(0, 0, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn.primary {
    background: linear-gradient(145deg,
        rgba(33, 150, 243, 0.9) 0%,
        rgba(25, 118, 210, 0.9) 50%,
        rgba(21, 101, 192, 0.9) 100%);
    border-color: #42a5f5;
    color: white;
}

.btn.primary:hover {
    background: linear-gradient(145deg,
        rgba(66, 165, 245, 0.9) 0%,
        rgba(33, 150, 243, 0.9) 50%,
        rgba(25, 118, 210, 0.9) 100%);
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
}

.btn.danger {
    background: linear-gradient(145deg,
        rgba(244, 67, 54, 0.9) 0%,
        rgba(211, 47, 47, 0.9) 50%,
        rgba(183, 28, 28, 0.9) 100%);
    border-color: #ef5350;
    color: white;
}

.btn.danger:hover {
    background: linear-gradient(145deg,
        rgba(239, 83, 80, 0.9) 0%,
        rgba(244, 67, 54, 0.9) 50%,
        rgba(211, 47, 47, 0.9) 100%);
    box-shadow: 0 0 20px rgba(244, 67, 54, 0.4);
}

/* 控制按钮组 */
.control-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.sound-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.sound-controls span {
    color: #aaa;
    font-size: 12px;
}

/* 统计面板 */
#stats {
    bottom: 20px;
    left: 20px;
    min-width: 200px;
}

/* 反应时间面板 */
.reaction-time {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    font-size: 14px;
    min-width: 200px;
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateY(-50%) translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateY(-50%) translateX(0);
        opacity: 1;
    }
}

/* 面板进入动画 */
.panel {
    animation: panelFadeIn 0.6s ease-out;
}

@keyframes panelFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 鼠标悬停时的面板效果 */
.panel:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.6),
        0 12px 40px rgba(0, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 加载动画 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 游戏开始时的全屏闪烁效果 */
@keyframes gameStart {
    0% { background-color: rgba(0, 255, 255, 0.1); }
    50% { background-color: rgba(0, 255, 255, 0.05); }
    100% { background-color: transparent; }
}

.game-starting {
    animation: gameStart 0.5s ease-out;
}

/* 时间紧迫时的警告效果 */
.time-warning {
    animation: timeWarning 1s ease-in-out infinite;
}

@keyframes timeWarning {
    0%, 100% {
        color: #ff4444 !important;
        text-shadow: 0 0 10px #ff4444;
    }
    50% {
        color: #ff8888 !important;
        text-shadow: 0 0 20px #ff4444;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .panel {
        padding: 12px;
        font-size: 13px;
    }

    #hud {
        max-width: 180px;
    }

    #settings {
        max-width: 200px;
    }

    #stats {
        max-width: 180px;
    }
}

@media (max-width: 768px) {
    .panel {
        padding: 10px;
        font-size: 12px;
        max-width: calc(50vw - 30px);
    }

    #hud {
        top: 10px;
        left: 10px;
        max-width: calc(45vw - 20px);
    }

    #settings {
        top: 10px;
        right: 10px;
        max-width: calc(50vw - 20px);
    }

    #stats {
        bottom: 10px;
        left: 10px;
        max-width: calc(45vw - 20px);
    }

    .reaction-time {
        display: none !important;
    }

    .combo-display {
        font-size: 36px;
    }

    .control-group {
        flex-direction: column;
        gap: 4px;
    }

    .sound-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .btn {
        padding: 6px 12px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .panel {
        padding: 8px;
        font-size: 10px;
        border-radius: 8px;
    }

    #hud, #settings, #stats {
        max-width: calc(100vw - 20px);
        position: fixed;
    }

    #hud {
        top: 5px;
        left: 5px;
        right: 5px;
        max-width: none;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    #hud .stat-item {
        flex: 1;
        min-width: 80px;
        margin-bottom: 0;
        text-align: center;
        border-bottom: none;
        border-right: 1px solid rgba(0, 255, 0, 0.2);
    }

    #hud .stat-item:last-child {
        border-right: none;
    }

    #settings {
        top: auto;
        bottom: 5px;
        left: 5px;
        right: 5px;
        max-width: none;
    }

    #stats {
        display: none; /* 在小屏幕上隐藏统计面板 */
    }

    .target.small { width: 35px; height: 35px; font-size: 10px; }
    .target.medium { width: 50px; height: 50px; font-size: 12px; }
    .target.large { width: 65px; height: 65px; font-size: 14px; }

    .combo-display {
        font-size: 24px;
    }

    .btn {
        padding: 4px 8px;
        font-size: 10px;
        margin: 1px;
    }

    .settings-section {
        margin-bottom: 8px;
        padding-bottom: 8px;
    }

    .settings-label {
        font-size: 10px;
        margin-bottom: 4px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
    .panel {
        padding: 6px;
        font-size: 9px;
    }

    .target.small { width: 30px; height: 30px; font-size: 8px; }
    .target.medium { width: 40px; height: 40px; font-size: 10px; }
    .target.large { width: 50px; height: 50px; font-size: 12px; }

    .combo-display {
        font-size: 20px;
    }
}

/* 启动画面 */
.splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #0c0c0c 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: splashFadeOut 2s ease-in-out 3s forwards;
}

.splash-screen h1 {
    font-family: 'Orbitron', sans-serif;
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 2s ease-in-out infinite, titlePulse 1s ease-in-out infinite;
    margin-bottom: 2rem;
    text-align: center;
}

.splash-screen p {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.5rem;
    color: #64b5f6;
    text-align: center;
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1s forwards;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #ff00ff);
    border-radius: 2px;
    animation: loadingProgress 3s ease-in-out forwards;
}

@keyframes splashFadeOut {
    to {
        opacity: 0;
        visibility: hidden;
    }
}

@keyframes titlePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes loadingProgress {
    from { width: 0%; }
    to { width: 100%; }
}

/* 成就通知 */
.achievement {
    position: fixed;
    top: 20px;
    right: -400px;
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.9));
    color: #000;
    padding: 15px 20px;
    border-radius: 10px;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    z-index: 1002;
    animation: achievementSlide 4s ease-in-out;
}

@keyframes achievementSlide {
    0%, 100% { transform: translateX(0); }
    10%, 90% { transform: translateX(-420px); }
}

/* 粒子效果增强 */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #00ffff, transparent);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 3s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-10vh) rotate(360deg);
        opacity: 0;
    }
}
