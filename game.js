/**
 * 射击定位训练器 - 游戏逻辑
 * 专业的射击精度训练工具
 */

// 游戏状态管理
class GameState {
    constructor() {
        this.score = 0;
        this.hits = 0;
        this.shots = 0;
        this.combo = 0;
        this.maxCombo = parseInt(localStorage.getItem('maxCombo')) || 0;
        this.timeLeft = 60;
        this.isPlaying = false;
        this.isPaused = false;
        this.difficulty = 'easy';
        this.bestScore = parseInt(localStorage.getItem('bestScore')) || 0;
        this.bestAccuracy = parseInt(localStorage.getItem('bestAccuracy')) || 0;
        this.soundEnabled = true;
        this.movingTargets = false;
        this.reactionTimes = [];
        this.bestReaction = parseFloat(localStorage.getItem('bestReaction')) || Infinity;
        this.targetSpawnTime = 0;
        this.gameTimer = null;
        this.spawnInterval = null;
    }

    reset() {
        this.score = 0;
        this.hits = 0;
        this.shots = 0;
        this.combo = 0;
        this.timeLeft = 60;
        this.reactionTimes = [];
        this.isPlaying = false;
        this.isPaused = false;
    }

    saveRecord(key, value) {
        localStorage.setItem(key, value);
    }

    updateBestRecords() {
        const accuracy = this.shots > 0 ? Math.round((this.hits / this.shots) * 100) : 0;
        
        if (this.score > this.bestScore) {
            this.bestScore = this.score;
            this.saveRecord('bestScore', this.bestScore);
        }
        
        if (accuracy > this.bestAccuracy) {
            this.bestAccuracy = accuracy;
            this.saveRecord('bestAccuracy', this.bestAccuracy);
        }
        
        if (this.combo > this.maxCombo) {
            this.maxCombo = this.combo;
            this.saveRecord('maxCombo', this.maxCombo);
        }
    }
}

// 难度配置
const DIFFICULTY_CONFIG = {
    easy: { 
        spawnRate: 1200, 
        targetLife: 3000, 
        targetSizes: ['large', 'medium'],
        description: '适合新手练习'
    },
    medium: { 
        spawnRate: 800, 
        targetLife: 2000, 
        targetSizes: ['medium', 'small'],
        description: '中等难度挑战'
    },
    hard: { 
        spawnRate: 500, 
        targetLife: 1500, 
        targetSizes: ['small'],
        description: '高难度训练'
    },
    extreme: { 
        spawnRate: 300, 
        targetLife: 1000, 
        targetSizes: ['small'],
        description: '极限挑战模式'
    }
};

// 目标配置
const TARGET_CONFIG = {
    small: { size: 40, points: 50, color: '#ff4444' },
    medium: { size: 60, points: 30, color: '#ffaa00' },
    large: { size: 80, points: 10, color: '#00ff00' }
};

// 音效系统
class AudioSystem {
    constructor() {
        this.context = null;
        this.enabled = true;
        this.initAudioContext();
    }

    initAudioContext() {
        try {
            this.context = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn('Web Audio API not supported');
        }
    }

    playSound(frequency, duration, type = 'sine', volume = 0.1) {
        if (!this.enabled || !this.context) return;

        try {
            const oscillator = this.context.createOscillator();
            const gainNode = this.context.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.context.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(volume, this.context.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.context.currentTime + duration);

            oscillator.start(this.context.currentTime);
            oscillator.stop(this.context.currentTime + duration);
        } catch (e) {
            console.warn('Audio playback failed:', e);
        }
    }

    playHitSound(combo = 0) {
        const baseFreq = 800;
        const comboBonus = Math.min(combo * 50, 500);
        this.playSound(baseFreq + comboBonus, 0.1);
    }

    playMissSound() {
        this.playSound(200, 0.2, 'sawtooth');
    }

    playComboSound() {
        this.playSound(1200, 0.3, 'square');
    }

    playStartSound() {
        setTimeout(() => this.playSound(440, 0.3), 0);
        setTimeout(() => this.playSound(554, 0.3), 100);
        setTimeout(() => this.playSound(659, 0.3), 200);
    }

    playEndSound() {
        this.playSound(330, 0.5);
        setTimeout(() => this.playSound(262, 0.8), 200);
    }

    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
}

// 游戏主类
class ShootingTrainer {
    constructor() {
        this.gameState = new GameState();
        this.audioSystem = new AudioSystem();
        this.gameArea = document.getElementById('gameArea');
        this.crosshair = document.getElementById('crosshair');
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateHUD();
        this.updateDifficultyButtons();
        this.startParticleSystem();
    }

    // 粒子系统
    startParticleSystem() {
        setInterval(() => {
            if (Math.random() < 0.3) {
                this.createParticle();
            }
        }, 2000);
    }

    createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.animationDelay = Math.random() * 2 + 's';

        const colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6b6b'];
        const color = colors[Math.floor(Math.random() * colors.length)];
        particle.style.background = `radial-gradient(circle, ${color}, transparent)`;

        document.body.appendChild(particle);

        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 3000);
    }

    // 成就系统
    checkAchievements() {
        const accuracy = this.gameState.shots > 0 ?
            Math.round((this.gameState.hits / this.gameState.shots) * 100) : 0;

        // 精度成就
        if (accuracy >= 90 && this.gameState.shots >= 10) {
            this.showAchievement('🎯 神枪手', '精度达到90%以上！');
        } else if (accuracy >= 80 && this.gameState.shots >= 10) {
            this.showAchievement('🏹 神射手', '精度达到80%以上！');
        }

        // 连击成就
        if (this.gameState.combo >= 20) {
            this.showAchievement('🔥 连击大师', '达成20连击！');
        } else if (this.gameState.combo >= 10) {
            this.showAchievement('⚡ 连击高手', '达成10连击！');
        }

        // 得分成就
        if (this.gameState.score >= 1000) {
            this.showAchievement('👑 得分王者', '得分超过1000分！');
        }
    }

    showAchievement(title, description) {
        const achievement = document.createElement('div');
        achievement.className = 'achievement';
        achievement.innerHTML = `
            <div style="font-size: 16px; margin-bottom: 5px;">${title}</div>
            <div style="font-size: 12px; opacity: 0.8;">${description}</div>
        `;

        document.body.appendChild(achievement);

        setTimeout(() => {
            if (achievement.parentNode) {
                achievement.parentNode.removeChild(achievement);
            }
        }, 4000);
    }

    setupEventListeners() {
        // 鼠标移动跟踪准星
        document.addEventListener('mousemove', (e) => {
            this.crosshair.style.left = e.clientX + 'px';
            this.crosshair.style.top = e.clientY + 'px';
        });

        // 点击事件
        document.addEventListener('click', (e) => this.handleClick(e));

        // 难度选择
        document.querySelectorAll('[data-difficulty]').forEach(btn => {
            btn.addEventListener('click', () => this.setDifficulty(btn.dataset.difficulty));
        });

        // 控制按钮
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetRecords());

        // 设置按钮
        document.getElementById('soundToggle').addEventListener('click', () => this.toggleSound());
        document.getElementById('movingToggle').addEventListener('click', () => this.toggleMovingTargets());

        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    handleClick(e) {
        if (!this.gameState.isPlaying || this.gameState.isPaused) return;

        const clickedElement = document.elementFromPoint(e.clientX, e.clientY);
        if (!clickedElement || !clickedElement.classList.contains('target')) {
            this.missShot(e.clientX, e.clientY);
        } else {
            this.gameState.shots++;
        }
    }

    handleKeyPress(e) {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                if (!this.gameState.isPlaying) {
                    this.startGame();
                } else {
                    this.togglePause();
                }
                break;
            case 'r':
            case 'R':
                if (!this.gameState.isPlaying) {
                    this.startGame();
                }
                break;
            case 'm':
            case 'M':
                this.toggleSound();
                break;
            case 'd':
            case 'D':
                // 开发者调试功能：显示禁区
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.showForbiddenAreas();
                    console.log('🔍 显示目标生成禁区（5秒后自动消失）');
                }
                break;
            case 'Escape':
                // ESC键暂停游戏
                if (this.gameState.isPlaying && !this.gameState.isPaused) {
                    this.togglePause();
                }
                break;
        }
    }

    setDifficulty(difficulty) {
        if (this.gameState.isPlaying) return;
        
        this.gameState.difficulty = difficulty;
        this.updateDifficultyButtons();
    }

    updateDifficultyButtons() {
        document.querySelectorAll('[data-difficulty]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.difficulty === this.gameState.difficulty);
        });
    }

    startGame() {
        this.gameState.reset();
        this.gameState.isPlaying = true;

        // 清除现有元素
        this.clearGameElements();

        // 添加游戏开始视觉效果
        document.body.classList.add('game-starting');
        setTimeout(() => {
            document.body.classList.remove('game-starting');
        }, 500);

        // 播放开始音效
        this.audioSystem.playStartSound();

        // 更新UI
        this.updateHUD();
        this.updateControlButtons();

        // 开始计时器和目标生成
        this.startTimer();
        this.startTargetSpawning();

        console.log('🎮 游戏开始！难度:', this.gameState.difficulty);
    }

    clearGameElements() {
        document.querySelectorAll('.target, .combo-display, .miss-indicator').forEach(el => el.remove());
    }

    startTimer() {
        this.gameState.gameTimer = setInterval(() => {
            if (!this.gameState.isPlaying || this.gameState.isPaused) return;
            
            this.gameState.timeLeft--;
            this.updateHUD();
            
            if (this.gameState.timeLeft <= 0) {
                this.endGame();
            }
        }, 1000);
    }

    startTargetSpawning() {
        const config = DIFFICULTY_CONFIG[this.gameState.difficulty];
        this.gameState.spawnInterval = setInterval(() => {
            if (!this.gameState.isPlaying || this.gameState.isPaused) return;
            this.createTarget();
        }, config.spawnRate);
    }

    endGame() {
        this.gameState.isPlaying = false;
        
        // 清除定时器
        if (this.gameState.gameTimer) {
            clearInterval(this.gameState.gameTimer);
        }
        if (this.gameState.spawnInterval) {
            clearInterval(this.gameState.spawnInterval);
        }
        
        // 播放结束音效
        this.audioSystem.playEndSound();
        
        // 更新记录
        this.gameState.updateBestRecords();
        this.updateHUD();
        this.updateControlButtons();
        
        // 显示结果
        this.showGameResult();
    }

    showGameResult() {
        const accuracy = this.gameState.shots > 0 ? 
            Math.round((this.gameState.hits / this.gameState.shots) * 100) : 0;
        const avgReaction = this.gameState.reactionTimes.length > 0 ? 
            Math.round(this.gameState.reactionTimes.reduce((a, b) => a + b, 0) / this.gameState.reactionTimes.length) : 0;
        
        let resultMessage = `🎯 训练结束！\n\n`;
        resultMessage += `📊 本次成绩:\n`;
        resultMessage += `得分: ${this.gameState.score}\n`;
        resultMessage += `精度: ${accuracy}%\n`;
        resultMessage += `命中: ${this.gameState.hits}/${this.gameState.shots}\n`;
        
        if (this.gameState.maxCombo > 0) {
            resultMessage += `最大连击: ${this.gameState.maxCombo}\n`;
        }
        if (avgReaction > 0) {
            resultMessage += `平均反应时间: ${avgReaction}ms\n`;
        }
        
        resultMessage += `\n🏆 历史最佳:\n`;
        resultMessage += `最高得分: ${this.gameState.bestScore}\n`;
        resultMessage += `最佳精度: ${this.gameState.bestAccuracy}%`;
        
        alert(resultMessage);
    }

    togglePause() {
        if (!this.gameState.isPlaying) return;
        
        this.gameState.isPaused = !this.gameState.isPaused;
        this.updateControlButtons();
    }

    updateControlButtons() {
        const pauseBtn = document.getElementById('pauseBtn');
        pauseBtn.textContent = this.gameState.isPaused ? '继续' : '暂停';
        pauseBtn.disabled = !this.gameState.isPlaying;
    }

    resetRecords() {
        if (confirm('⚠️ 确定要重置所有记录吗？\n\n这将清除:\n• 最高得分\n• 最佳精度\n• 最大连击\n• 最快反应时间')) {
            localStorage.clear();
            this.gameState.bestScore = 0;
            this.gameState.bestAccuracy = 0;
            this.gameState.maxCombo = 0;
            this.gameState.bestReaction = Infinity;
            this.updateHUD();
            alert('✅ 记录已重置！');
        }
    }

    toggleSound() {
        const enabled = this.audioSystem.toggle();
        const btn = document.getElementById('soundToggle');
        btn.textContent = enabled ? '开启' : '关闭';
        btn.classList.toggle('active', enabled);
    }

    toggleMovingTargets() {
        this.gameState.movingTargets = !this.gameState.movingTargets;
        const btn = document.getElementById('movingToggle');
        btn.textContent = this.gameState.movingTargets ? '开启' : '关闭';
        btn.classList.toggle('active', this.gameState.movingTargets);
    }

    createTarget() {
        if (!this.gameState.isPlaying || this.gameState.isPaused) return;

        const config = DIFFICULTY_CONFIG[this.gameState.difficulty];
        const sizeType = config.targetSizes[Math.floor(Math.random() * config.targetSizes.length)];
        const targetConfig = TARGET_CONFIG[sizeType];

        const target = document.createElement('div');
        target.className = `target ${sizeType}`;

        // 添加移动效果
        if (this.gameState.movingTargets && Math.random() < 0.3) {
            target.classList.add('moving-target');
        }

        // 计算安全生成区域，避开UI面板
        const position = this.calculateSafePosition(targetConfig.size);

        target.style.left = position.x + 'px';
        target.style.top = position.y + 'px';
        target.textContent = targetConfig.points;

        // 记录目标创建时间
        const spawnTime = Date.now();

        // 添加点击事件
        target.addEventListener('click', (e) => {
            e.stopPropagation();
            const reactionTime = Date.now() - spawnTime;
            this.hitTarget(target, targetConfig.points, reactionTime);
        });

        this.gameArea.appendChild(target);

        // 设置自动消失
        setTimeout(() => {
            if (target.parentNode) {
                target.parentNode.removeChild(target);
            }
        }, config.targetLife);
    }

    calculateSafePosition(targetSize) {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        // 动态计算UI面板的实际尺寸和位置
        const forbiddenAreas = this.getForbiddenAreas(screenWidth, screenHeight);

        // 为移动目标预留额外空间
        const movingMargin = this.gameState.movingTargets ? 180 : 20;

        let attempts = 0;
        const maxAttempts = 100;

        while (attempts < maxAttempts) {
            // 生成随机位置
            const x = Math.random() * (screenWidth - targetSize - movingMargin * 2) + movingMargin;
            const y = Math.random() * (screenHeight - targetSize - movingMargin * 2) + movingMargin;

            // 检查是否与禁区重叠
            const isInForbiddenArea = forbiddenAreas.some(area => {
                const buffer = 25; // 缓冲区
                return x < area.x + area.width + buffer &&
                       x + targetSize > area.x - buffer &&
                       y < area.y + area.height + buffer &&
                       y + targetSize > area.y - buffer;
            });

            // 如果不在禁区内，返回这个位置
            if (!isInForbiddenArea) {
                return { x, y };
            }

            attempts++;
        }

        // 如果尝试多次都失败，使用安全的中央区域
        return this.getFallbackPosition(screenWidth, screenHeight, targetSize);
    }

    getForbiddenAreas(screenWidth, screenHeight) {
        const areas = [];

        // 根据屏幕尺寸动态调整禁区
        if (screenWidth <= 480) {
            // 小屏幕：只保护顶部和底部的UI
            areas.push(
                { x: 0, y: 0, width: screenWidth, height: 80 }, // 顶部HUD
                { x: 0, y: screenHeight - 120, width: screenWidth, height: 120 } // 底部设置
            );
        } else if (screenWidth <= 768) {
            // 中等屏幕
            areas.push(
                { x: 0, y: 0, width: Math.min(200, screenWidth * 0.45), height: 140 }, // HUD
                { x: screenWidth - Math.min(220, screenWidth * 0.5), y: 0, width: Math.min(220, screenWidth * 0.5), height: 160 }, // 设置
                { x: 0, y: screenHeight - 120, width: Math.min(200, screenWidth * 0.45), height: 120 } // 统计
            );
        } else {
            // 大屏幕：完整的UI面板保护
            areas.push(
                { x: 0, y: 0, width: 260, height: 180 }, // HUD面板
                { x: screenWidth - 280, y: 0, width: 280, height: 220 }, // 设置面板
                { x: 0, y: screenHeight - 160, width: 260, height: 160 }, // 统计面板
                { x: screenWidth - 220, y: screenHeight * 0.25, width: 220, height: screenHeight * 0.5 } // 反应时间面板
            );
        }

        return areas;
    }

    getFallbackPosition(screenWidth, screenHeight, targetSize) {
        // 计算屏幕中央的安全区域
        const centerX = screenWidth / 2;
        const centerY = screenHeight / 2;

        // 在中央区域随机生成位置
        const safeZoneSize = Math.min(screenWidth, screenHeight) * 0.3;
        const offsetX = (Math.random() - 0.5) * safeZoneSize;
        const offsetY = (Math.random() - 0.5) * safeZoneSize;

        return {
            x: Math.max(targetSize, Math.min(screenWidth - targetSize, centerX + offsetX - targetSize / 2)),
            y: Math.max(targetSize, Math.min(screenHeight - targetSize, centerY + offsetY - targetSize / 2))
        };
    }

    // 调试功能：显示禁区（开发时使用）
    showForbiddenAreas() {
        // 移除现有的调试元素
        document.querySelectorAll('.debug-forbidden-area').forEach(el => el.remove());

        const areas = this.getForbiddenAreas(window.innerWidth, window.innerHeight);

        areas.forEach((area, index) => {
            const debugDiv = document.createElement('div');
            debugDiv.className = 'debug-forbidden-area';
            debugDiv.style.cssText = `
                position: fixed;
                left: ${area.x}px;
                top: ${area.y}px;
                width: ${area.width}px;
                height: ${area.height}px;
                background: rgba(255, 0, 0, 0.2);
                border: 2px dashed red;
                pointer-events: none;
                z-index: 10000;
                font-size: 12px;
                color: red;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            debugDiv.textContent = `禁区 ${index + 1}`;
            document.body.appendChild(debugDiv);
        });

        // 5秒后自动移除
        setTimeout(() => {
            document.querySelectorAll('.debug-forbidden-area').forEach(el => el.remove());
        }, 5000);
    }

    hitTarget(target, points, reactionTime) {
        target.classList.add('hit-animation');

        // 更新游戏状态
        this.gameState.hits++;
        this.gameState.combo++;
        this.gameState.shots++;

        // 计算得分（包含连击奖励）
        const comboBonus = this.gameState.combo > 5 ? Math.floor(this.gameState.combo / 5) * 10 : 0;
        this.gameState.score += points + comboBonus;

        // 记录反应时间
        if (reactionTime) {
            this.gameState.reactionTimes.push(reactionTime);
            if (reactionTime < this.gameState.bestReaction) {
                this.gameState.bestReaction = reactionTime;
                this.gameState.saveRecord('bestReaction', this.gameState.bestReaction);
            }
        }

        // 更新最大连击记录
        if (this.gameState.combo > this.gameState.maxCombo) {
            this.gameState.maxCombo = this.gameState.combo;
            this.gameState.saveRecord('maxCombo', this.gameState.maxCombo);
        }

        // 播放音效
        this.audioSystem.playHitSound(this.gameState.combo);

        // 显示连击效果
        if (this.gameState.combo % 5 === 0 && this.gameState.combo >= 5) {
            this.showComboEffect(this.gameState.combo);
            this.audioSystem.playComboSound();
        }

        // 移除目标
        setTimeout(() => {
            if (target.parentNode) {
                target.parentNode.removeChild(target);
            }
        }, 300);

        this.updateHUD();
        this.checkAchievements();
    }

    missShot(x, y) {
        this.gameState.shots++;
        this.gameState.combo = 0;

        // 播放未命中音效
        this.audioSystem.playMissSound();

        // 创建未命中指示器
        const missIndicator = document.createElement('div');
        missIndicator.className = 'miss-indicator';
        missIndicator.style.left = (x - 10) + 'px';
        missIndicator.style.top = (y - 10) + 'px';

        this.gameArea.appendChild(missIndicator);

        setTimeout(() => {
            if (missIndicator.parentNode) {
                missIndicator.parentNode.removeChild(missIndicator);
            }
        }, 600);

        this.updateHUD();
    }

    showComboEffect(combo) {
        if (combo < 5) return;

        const comboDiv = document.createElement('div');
        comboDiv.className = 'combo-display';
        comboDiv.textContent = `${combo}x 连击!`;

        // 添加随机颜色效果
        const colors = ['#ffff00', '#ff6600', '#ff0066', '#6600ff', '#00ff66'];
        comboDiv.style.color = colors[Math.floor(Math.random() * colors.length)];

        document.body.appendChild(comboDiv);

        setTimeout(() => {
            if (comboDiv.parentNode) {
                comboDiv.parentNode.removeChild(comboDiv);
            }
        }, 1200);
    }

    updateHUD() {
        // 基础统计
        document.getElementById('score').textContent = this.gameState.score.toLocaleString();
        document.getElementById('hits').textContent = this.gameState.hits;
        document.getElementById('shots').textContent = this.gameState.shots;
        document.getElementById('timer').textContent = this.gameState.timeLeft;
        document.getElementById('combo').textContent = this.gameState.combo;
        document.getElementById('maxCombo').textContent = this.gameState.maxCombo;

        // 计算精度
        const accuracy = this.gameState.shots > 0 ?
            Math.round((this.gameState.hits / this.gameState.shots) * 100) : 0;
        document.getElementById('accuracy').textContent = accuracy + '%';

        // 最佳记录
        document.getElementById('bestScore').textContent = this.gameState.bestScore.toLocaleString();
        document.getElementById('bestAccuracy').textContent = this.gameState.bestAccuracy + '%';

        // 反应时间统计
        if (this.gameState.reactionTimes.length > 0) {
            const avgReaction = Math.round(
                this.gameState.reactionTimes.reduce((a, b) => a + b, 0) / this.gameState.reactionTimes.length
            );
            document.getElementById('avgReaction').textContent = avgReaction;
            document.getElementById('bestReaction').textContent =
                this.gameState.bestReaction === Infinity ? '∞' : Math.round(this.gameState.bestReaction);
            document.getElementById('reactionTime').style.display = 'block';
        }

        // 更新时间颜色（时间紧迫时变红）
        const timerElement = document.getElementById('timer');
        const timerContainer = timerElement.parentElement;

        if (this.gameState.timeLeft <= 10 && this.gameState.isPlaying) {
            timerContainer.classList.add('time-warning');
        } else {
            timerContainer.classList.remove('time-warning');
        }

        // 精度颜色动态变化
        const accuracyElement = document.getElementById('accuracy');
        const currentAccuracy = this.gameState.shots > 0 ?
            Math.round((this.gameState.hits / this.gameState.shots) * 100) : 0;

        if (currentAccuracy >= 80) {
            accuracyElement.style.color = '#4caf50';
        } else if (currentAccuracy >= 60) {
            accuracyElement.style.color = '#ff9800';
        } else {
            accuracyElement.style.color = '#f44336';
        }
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    window.game = new ShootingTrainer();

    // 添加脉冲动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    `;
    document.head.appendChild(style);

    console.log('🎯 射击定位训练器已加载');
    console.log('快捷键: 空格键开始/暂停, R键重新开始, M键切换音效');
});
