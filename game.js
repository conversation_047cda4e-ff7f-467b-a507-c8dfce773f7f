/**
 * 射击定位训练器 - 游戏逻辑
 * 专业的射击精度训练工具
 */

// 游戏状态管理
class GameState {
    constructor() {
        this.score = 0;
        this.hits = 0;
        this.shots = 0;
        this.combo = 0;
        this.maxCombo = parseInt(localStorage.getItem('maxCombo')) || 0;
        this.timeLeft = 60;
        this.isPlaying = false;
        this.isPaused = false;
        this.difficulty = 'easy';
        this.bestScore = parseInt(localStorage.getItem('bestScore')) || 0;
        this.bestAccuracy = parseInt(localStorage.getItem('bestAccuracy')) || 0;
        this.soundEnabled = true;
        this.movingTargets = false;
        this.reactionTimes = [];
        this.bestReaction = parseFloat(localStorage.getItem('bestReaction')) || Infinity;
        this.targetSpawnTime = 0;
        this.gameTimer = null;
        this.spawnInterval = null;
    }

    reset() {
        this.score = 0;
        this.hits = 0;
        this.shots = 0;
        this.combo = 0;
        this.timeLeft = 60;
        this.reactionTimes = [];
        this.isPlaying = false;
        this.isPaused = false;
    }

    saveRecord(key, value) {
        localStorage.setItem(key, value);
    }

    updateBestRecords() {
        const accuracy = this.shots > 0 ? Math.round((this.hits / this.shots) * 100) : 0;
        
        if (this.score > this.bestScore) {
            this.bestScore = this.score;
            this.saveRecord('bestScore', this.bestScore);
        }
        
        if (accuracy > this.bestAccuracy) {
            this.bestAccuracy = accuracy;
            this.saveRecord('bestAccuracy', this.bestAccuracy);
        }
        
        if (this.combo > this.maxCombo) {
            this.maxCombo = this.combo;
            this.saveRecord('maxCombo', this.maxCombo);
        }
    }
}

// 难度配置
const DIFFICULTY_CONFIG = {
    easy: { 
        spawnRate: 1200, 
        targetLife: 3000, 
        targetSizes: ['large', 'medium'],
        description: '适合新手练习'
    },
    medium: { 
        spawnRate: 800, 
        targetLife: 2000, 
        targetSizes: ['medium', 'small'],
        description: '中等难度挑战'
    },
    hard: { 
        spawnRate: 500, 
        targetLife: 1500, 
        targetSizes: ['small'],
        description: '高难度训练'
    },
    extreme: { 
        spawnRate: 300, 
        targetLife: 1000, 
        targetSizes: ['small'],
        description: '极限挑战模式'
    }
};

// 目标配置
const TARGET_CONFIG = {
    small: { size: 40, points: 50, color: '#ff4444' },
    medium: { size: 60, points: 30, color: '#ffaa00' },
    large: { size: 80, points: 10, color: '#00ff00' }
};

// 音效系统
class AudioSystem {
    constructor() {
        this.context = null;
        this.enabled = true;
        this.initAudioContext();
    }

    initAudioContext() {
        try {
            this.context = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn('Web Audio API not supported');
        }
    }

    playSound(frequency, duration, type = 'sine', volume = 0.1) {
        if (!this.enabled || !this.context) return;

        try {
            const oscillator = this.context.createOscillator();
            const gainNode = this.context.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.context.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(volume, this.context.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.context.currentTime + duration);

            oscillator.start(this.context.currentTime);
            oscillator.stop(this.context.currentTime + duration);
        } catch (e) {
            console.warn('Audio playback failed:', e);
        }
    }

    playHitSound(combo = 0) {
        const baseFreq = 800;
        const comboBonus = Math.min(combo * 50, 500);
        this.playSound(baseFreq + comboBonus, 0.1);
    }

    playMissSound() {
        this.playSound(200, 0.2, 'sawtooth');
    }

    playComboSound() {
        this.playSound(1200, 0.3, 'square');
    }

    playStartSound() {
        setTimeout(() => this.playSound(440, 0.3), 0);
        setTimeout(() => this.playSound(554, 0.3), 100);
        setTimeout(() => this.playSound(659, 0.3), 200);
    }

    playEndSound() {
        this.playSound(330, 0.5);
        setTimeout(() => this.playSound(262, 0.8), 200);
    }

    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
}

// 游戏主类
class ShootingTrainer {
    constructor() {
        this.gameState = new GameState();
        this.audioSystem = new AudioSystem();
        this.gameArea = document.getElementById('gameArea');
        this.crosshair = document.getElementById('crosshair');
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateHUD();
        this.updateDifficultyButtons();
    }

    setupEventListeners() {
        // 鼠标移动跟踪准星
        document.addEventListener('mousemove', (e) => {
            this.crosshair.style.left = e.clientX + 'px';
            this.crosshair.style.top = e.clientY + 'px';
        });

        // 点击事件
        document.addEventListener('click', (e) => this.handleClick(e));

        // 难度选择
        document.querySelectorAll('[data-difficulty]').forEach(btn => {
            btn.addEventListener('click', () => this.setDifficulty(btn.dataset.difficulty));
        });

        // 控制按钮
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetRecords());

        // 设置按钮
        document.getElementById('soundToggle').addEventListener('click', () => this.toggleSound());
        document.getElementById('movingToggle').addEventListener('click', () => this.toggleMovingTargets());

        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    handleClick(e) {
        if (!this.gameState.isPlaying || this.gameState.isPaused) return;

        const clickedElement = document.elementFromPoint(e.clientX, e.clientY);
        if (!clickedElement || !clickedElement.classList.contains('target')) {
            this.missShot(e.clientX, e.clientY);
        } else {
            this.gameState.shots++;
        }
    }

    handleKeyPress(e) {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                if (!this.gameState.isPlaying) {
                    this.startGame();
                } else {
                    this.togglePause();
                }
                break;
            case 'r':
            case 'R':
                if (!this.gameState.isPlaying) {
                    this.startGame();
                }
                break;
            case 'm':
            case 'M':
                this.toggleSound();
                break;
        }
    }

    setDifficulty(difficulty) {
        if (this.gameState.isPlaying) return;
        
        this.gameState.difficulty = difficulty;
        this.updateDifficultyButtons();
    }

    updateDifficultyButtons() {
        document.querySelectorAll('[data-difficulty]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.difficulty === this.gameState.difficulty);
        });
    }

    startGame() {
        this.gameState.reset();
        this.gameState.isPlaying = true;
        
        // 清除现有元素
        this.clearGameElements();
        
        // 播放开始音效
        this.audioSystem.playStartSound();
        
        // 更新UI
        this.updateHUD();
        this.updateControlButtons();
        
        // 开始计时器和目标生成
        this.startTimer();
        this.startTargetSpawning();
    }

    clearGameElements() {
        document.querySelectorAll('.target, .combo-display, .miss-indicator').forEach(el => el.remove());
    }

    startTimer() {
        this.gameState.gameTimer = setInterval(() => {
            if (!this.gameState.isPlaying || this.gameState.isPaused) return;
            
            this.gameState.timeLeft--;
            this.updateHUD();
            
            if (this.gameState.timeLeft <= 0) {
                this.endGame();
            }
        }, 1000);
    }

    startTargetSpawning() {
        const config = DIFFICULTY_CONFIG[this.gameState.difficulty];
        this.gameState.spawnInterval = setInterval(() => {
            if (!this.gameState.isPlaying || this.gameState.isPaused) return;
            this.createTarget();
        }, config.spawnRate);
    }

    endGame() {
        this.gameState.isPlaying = false;
        
        // 清除定时器
        if (this.gameState.gameTimer) {
            clearInterval(this.gameState.gameTimer);
        }
        if (this.gameState.spawnInterval) {
            clearInterval(this.gameState.spawnInterval);
        }
        
        // 播放结束音效
        this.audioSystem.playEndSound();
        
        // 更新记录
        this.gameState.updateBestRecords();
        this.updateHUD();
        this.updateControlButtons();
        
        // 显示结果
        this.showGameResult();
    }

    showGameResult() {
        const accuracy = this.gameState.shots > 0 ? 
            Math.round((this.gameState.hits / this.gameState.shots) * 100) : 0;
        const avgReaction = this.gameState.reactionTimes.length > 0 ? 
            Math.round(this.gameState.reactionTimes.reduce((a, b) => a + b, 0) / this.gameState.reactionTimes.length) : 0;
        
        let resultMessage = `🎯 训练结束！\n\n`;
        resultMessage += `📊 本次成绩:\n`;
        resultMessage += `得分: ${this.gameState.score}\n`;
        resultMessage += `精度: ${accuracy}%\n`;
        resultMessage += `命中: ${this.gameState.hits}/${this.gameState.shots}\n`;
        
        if (this.gameState.maxCombo > 0) {
            resultMessage += `最大连击: ${this.gameState.maxCombo}\n`;
        }
        if (avgReaction > 0) {
            resultMessage += `平均反应时间: ${avgReaction}ms\n`;
        }
        
        resultMessage += `\n🏆 历史最佳:\n`;
        resultMessage += `最高得分: ${this.gameState.bestScore}\n`;
        resultMessage += `最佳精度: ${this.gameState.bestAccuracy}%`;
        
        alert(resultMessage);
    }

    togglePause() {
        if (!this.gameState.isPlaying) return;
        
        this.gameState.isPaused = !this.gameState.isPaused;
        this.updateControlButtons();
    }

    updateControlButtons() {
        const pauseBtn = document.getElementById('pauseBtn');
        pauseBtn.textContent = this.gameState.isPaused ? '继续' : '暂停';
        pauseBtn.disabled = !this.gameState.isPlaying;
    }

    resetRecords() {
        if (confirm('⚠️ 确定要重置所有记录吗？\n\n这将清除:\n• 最高得分\n• 最佳精度\n• 最大连击\n• 最快反应时间')) {
            localStorage.clear();
            this.gameState.bestScore = 0;
            this.gameState.bestAccuracy = 0;
            this.gameState.maxCombo = 0;
            this.gameState.bestReaction = Infinity;
            this.updateHUD();
            alert('✅ 记录已重置！');
        }
    }

    toggleSound() {
        const enabled = this.audioSystem.toggle();
        const btn = document.getElementById('soundToggle');
        btn.textContent = enabled ? '开启' : '关闭';
        btn.classList.toggle('active', enabled);
    }

    toggleMovingTargets() {
        this.gameState.movingTargets = !this.gameState.movingTargets;
        const btn = document.getElementById('movingToggle');
        btn.textContent = this.gameState.movingTargets ? '开启' : '关闭';
        btn.classList.toggle('active', this.gameState.movingTargets);
    }

    createTarget() {
        if (!this.gameState.isPlaying || this.gameState.isPaused) return;

        const config = DIFFICULTY_CONFIG[this.gameState.difficulty];
        const sizeType = config.targetSizes[Math.floor(Math.random() * config.targetSizes.length)];
        const targetConfig = TARGET_CONFIG[sizeType];

        const target = document.createElement('div');
        target.className = `target ${sizeType}`;

        // 添加移动效果
        if (this.gameState.movingTargets && Math.random() < 0.3) {
            target.classList.add('moving-target');
        }

        // 计算位置（为移动目标留出空间）
        const margin = this.gameState.movingTargets ? 200 : 50;
        const x = Math.random() * (window.innerWidth - targetConfig.size - margin);
        const y = Math.random() * (window.innerHeight - targetConfig.size - margin);

        target.style.left = x + 'px';
        target.style.top = y + 'px';
        target.textContent = targetConfig.points;

        // 记录目标创建时间
        const spawnTime = Date.now();

        // 添加点击事件
        target.addEventListener('click', (e) => {
            e.stopPropagation();
            const reactionTime = Date.now() - spawnTime;
            this.hitTarget(target, targetConfig.points, reactionTime);
        });

        this.gameArea.appendChild(target);

        // 设置自动消失
        setTimeout(() => {
            if (target.parentNode) {
                target.parentNode.removeChild(target);
            }
        }, config.targetLife);
    }

    hitTarget(target, points, reactionTime) {
        target.classList.add('hit-animation');

        // 更新游戏状态
        this.gameState.hits++;
        this.gameState.combo++;
        this.gameState.shots++;

        // 计算得分（包含连击奖励）
        const comboBonus = this.gameState.combo > 5 ? Math.floor(this.gameState.combo / 5) * 10 : 0;
        this.gameState.score += points + comboBonus;

        // 记录反应时间
        if (reactionTime) {
            this.gameState.reactionTimes.push(reactionTime);
            if (reactionTime < this.gameState.bestReaction) {
                this.gameState.bestReaction = reactionTime;
                this.gameState.saveRecord('bestReaction', this.gameState.bestReaction);
            }
        }

        // 更新最大连击记录
        if (this.gameState.combo > this.gameState.maxCombo) {
            this.gameState.maxCombo = this.gameState.combo;
            this.gameState.saveRecord('maxCombo', this.gameState.maxCombo);
        }

        // 播放音效
        this.audioSystem.playHitSound(this.gameState.combo);

        // 显示连击效果
        if (this.gameState.combo % 5 === 0 && this.gameState.combo >= 5) {
            this.showComboEffect(this.gameState.combo);
            this.audioSystem.playComboSound();
        }

        // 移除目标
        setTimeout(() => {
            if (target.parentNode) {
                target.parentNode.removeChild(target);
            }
        }, 300);

        this.updateHUD();
    }

    missShot(x, y) {
        this.gameState.shots++;
        this.gameState.combo = 0;

        // 播放未命中音效
        this.audioSystem.playMissSound();

        // 创建未命中指示器
        const missIndicator = document.createElement('div');
        missIndicator.className = 'miss-indicator';
        missIndicator.style.left = (x - 10) + 'px';
        missIndicator.style.top = (y - 10) + 'px';

        this.gameArea.appendChild(missIndicator);

        setTimeout(() => {
            if (missIndicator.parentNode) {
                missIndicator.parentNode.removeChild(missIndicator);
            }
        }, 600);

        this.updateHUD();
    }

    showComboEffect(combo) {
        if (combo < 5) return;

        const comboDiv = document.createElement('div');
        comboDiv.className = 'combo-display';
        comboDiv.textContent = `${combo}x 连击!`;

        // 添加随机颜色效果
        const colors = ['#ffff00', '#ff6600', '#ff0066', '#6600ff', '#00ff66'];
        comboDiv.style.color = colors[Math.floor(Math.random() * colors.length)];

        document.body.appendChild(comboDiv);

        setTimeout(() => {
            if (comboDiv.parentNode) {
                comboDiv.parentNode.removeChild(comboDiv);
            }
        }, 1200);
    }

    updateHUD() {
        // 基础统计
        document.getElementById('score').textContent = this.gameState.score.toLocaleString();
        document.getElementById('hits').textContent = this.gameState.hits;
        document.getElementById('shots').textContent = this.gameState.shots;
        document.getElementById('timer').textContent = this.gameState.timeLeft;
        document.getElementById('combo').textContent = this.gameState.combo;
        document.getElementById('maxCombo').textContent = this.gameState.maxCombo;

        // 计算精度
        const accuracy = this.gameState.shots > 0 ?
            Math.round((this.gameState.hits / this.gameState.shots) * 100) : 0;
        document.getElementById('accuracy').textContent = accuracy + '%';

        // 最佳记录
        document.getElementById('bestScore').textContent = this.gameState.bestScore.toLocaleString();
        document.getElementById('bestAccuracy').textContent = this.gameState.bestAccuracy + '%';

        // 反应时间统计
        if (this.gameState.reactionTimes.length > 0) {
            const avgReaction = Math.round(
                this.gameState.reactionTimes.reduce((a, b) => a + b, 0) / this.gameState.reactionTimes.length
            );
            document.getElementById('avgReaction').textContent = avgReaction;
            document.getElementById('bestReaction').textContent =
                this.gameState.bestReaction === Infinity ? '∞' : Math.round(this.gameState.bestReaction);
            document.getElementById('reactionTime').style.display = 'block';
        }

        // 更新时间颜色（时间紧迫时变红）
        const timerElement = document.getElementById('timer');
        if (this.gameState.timeLeft <= 10 && this.gameState.isPlaying) {
            timerElement.style.color = '#ff4444';
            timerElement.style.animation = 'pulse 1s infinite';
        } else {
            timerElement.style.color = '#00ff00';
            timerElement.style.animation = 'none';
        }
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    window.game = new ShootingTrainer();

    // 添加脉冲动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    `;
    document.head.appendChild(style);

    console.log('🎯 射击定位训练器已加载');
    console.log('快捷键: 空格键开始/暂停, R键重新开始, M键切换音效');
});
